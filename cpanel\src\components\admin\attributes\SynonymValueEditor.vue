<template>
  <div class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-5 gap-2 items-end">
      <VInputText v-model="newValue" placeholder="Значение" class="w-full md:col-span-1" @keyup.enter="addValue" />
      <VInputText v-model="newNotes" placeholder="Заметки (опц.)" class="w-full md:col-span-1" @keyup.enter="addValue" />
      <VSelect v-model="newBrandId" :options="brandOptions" option-label="name" option-value="id" class="w-full md:col-span-1" placeholder="Бренд (опц.)" />
      <VSelect v-model="newLevel" :options="compatibilityOptions" option-label="label" option-value="value" class="w-full md:col-span-1" />
      <VButton  @click="addValue" :disabled="!canAdd">
        <PlusIcon class="w-4 h-4" />
      </VButton>
    </div>
    <VDataTable :value="synonyms" :loading="loading" class="p-datatable-sm" table-style="min-width: 44rem" striped-rows>
      <Column field="value" header="Значение"></Column>
      <Column field="brandId" header="Бренд">
        <template #body="{ data }">
          <span>{{ brandName(data.brandId) }}</span>
        </template>
      </Column>
      <Column field="compatibilityLevel" header="Уровень"></Column>
      <Column field="notes" header="Заметки"></Column>
      <Column header="" style="width: 120px">
        <template #body="{ data }">
          <div class="flex gap-2">
            <VButton size="small" severity="secondary" outlined @click="editRow(data)">
              <PencilIcon class="w-4 h-4" />
            </VButton>
            <DangerButton size="small"  outlined @click="removeValue(data)">
              <TrashIcon class="w-4 h-4" />
            </DangerButton>
          </div>
        </template>
      </Column>
    </VDataTable>

    <VDialog v-model:visible="showEdit" modal header="Редактировать синоним" :style="{ width: '28rem' }">
      <div class="space-y-3">
        <VSelect v-model="editForm.brandId" :options="brandOptions" option-label="name" option-value="id" class="w-full" placeholder="Бренд (опц.)" />
        <VInputText v-model="editForm.notes" placeholder="Заметки" class="w-full" />
        <VSelect v-model="editForm.compatibilityLevel" :options="compatibilityOptions" option-label="label" option-value="value" class="w-full" />
      </div>
      <template #footer>
        <VButton label="Отмена" severity="secondary" @click="showEdit=false" />
        <VButton label="Сохранить" @click="saveEdit" />
      </template>
    </VDialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import VInputText from '@/volt/InputText.vue'
import VButton from '@/volt/Button.vue'
import VDataTable from '@/volt/DataTable.vue'
import VDialog from '@/volt/Dialog.vue'
import VSelect from '@/volt/Select.vue'
import Column from 'primevue/column'
import { useToast } from '@/composables/useToast'
import { useTrpc } from '@/composables/useTrpc'
import Icon from '@/components/ui/Icon.vue'
import { PlusIcon } from 'lucide-vue-next'
import { PencilIcon } from 'lucide-vue-next'
import { TrashIcon } from 'lucide-vue-next'
import DangerButton from '@/volt/DangerButton.vue'

const props = defineProps<{ groupId: number }>()

const { attributeSynonyms, brands } = useTrpc()
const toast = useToast()

const synonyms = ref<Array<{ id: number; value: string; notes?: string|null; brandId?: number|null; compatibilityLevel?: 'EXACT'|'NEAR'|'LEGACY' }>>([])
const loading = ref(false)
const newValue = ref('')
const newNotes = ref<string | null>(null)
const newLevel = ref<'EXACT'|'NEAR'|'LEGACY' | null>(null)
const newBrandId = ref<number | null>(null)

const compatibilityOptions = [
  { label: 'EXACT', value: 'EXACT' },
  { label: 'NEAR', value: 'NEAR' },
  { label: 'LEGACY', value: 'LEGACY' },
]

const brandOptions = ref<Array<{id:number; name:string}>>([])
const loadBrands = async () => {
  const res = await brands.findMany({})
  brandOptions.value = Array.isArray(res) ? (res as any).map((b: any) => ({ id: b.id, name: b.name })) : []
}
const brandName = (id?: number|null) => brandOptions.value.find(b => b.id === id)?.name || ''

const canAdd = ref(false)
watch([newValue, newNotes, newLevel, newBrandId], () => {
  const trimmed = (newValue.value || '').trim()
  canAdd.value = trimmed.length > 0 && !synonyms.value.some(s => s.value.toLowerCase() === trimmed.toLowerCase())
})

const load = async () => {
  loading.value = true
  try {
    await loadBrands()
    const result = await attributeSynonyms.synonyms.findMany({ groupId: props.groupId })
    if (Array.isArray(result)) synonyms.value = result as any
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось загрузить значения')
  } finally {
    loading.value = false
  }
}

const addValue = async () => {
  const value = newValue.value.trim()
  if (!value) return
  if (synonyms.value.some(s => s.value.toLowerCase() === value.toLowerCase())) {
    toast.error('Дубликаты не допускаются')
    return
  }
  try {
    const created = await attributeSynonyms.synonyms.create({ groupId: props.groupId, value, notes: newNotes.value, brandId: newBrandId.value ?? undefined, compatibilityLevel: newLevel.value ?? undefined })
    if (created && typeof created === 'object') {
      synonyms.value.push(created as any)
      newValue.value = ''
      newNotes.value = null
      newLevel.value = null
      newBrandId.value = null
    }
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось добавить значение')
  }
}

const showEdit = ref(false)
const editForm = ref<{ id: number; notes: string | null; brandId: number | null; compatibilityLevel: 'EXACT'|'NEAR'|'LEGACY' | null }>({ id: 0, notes: null, brandId: null, compatibilityLevel: null })

const editRow = (row: any) => {
  editForm.value = { id: row.id, notes: row.notes ?? null, brandId: row.brandId ?? null, compatibilityLevel: (row.compatibilityLevel ?? null) }
  showEdit.value = true
}

const saveEdit = async () => {
  try {
    const updated = await attributeSynonyms.synonyms.update({ id: editForm.value.id, notes: editForm.value.notes, brandId: editForm.value.brandId ?? undefined, compatibilityLevel: editForm.value.compatibilityLevel ?? undefined })
    if (updated) {
      const idx = synonyms.value.findIndex(s => s.id === editForm.value.id)
      if (idx !== -1) synonyms.value[idx] = { ...synonyms.value[idx], ...updated as any }
      showEdit.value = false
    }
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось сохранить изменения')
  }
}

const removeValue = async (row: { id: number }) => {
  if (!confirm('Удалить значение?')) return
  try {
    await attributeSynonyms.synonyms.delete({ id: row.id })
    synonyms.value = synonyms.value.filter(s => s.id !== row.id)
  } catch (e: any) {
    toast.error(e?.message || 'Не удалось удалить значение')
  }
}

onMounted(load)
watch(() => props.groupId, load)
</script>


